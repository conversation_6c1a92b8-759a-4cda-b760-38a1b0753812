// SPDX-License-Identifier: MIT
pragma solidity ^0.8.25;

import { TimelockController } from "@openzeppelin/contracts/governance/TimelockController.sol";
import { IERC20 } from "@openzeppelin/contracts/interfaces/IERC20.sol";
import { Script } from "forge-std/Script.sol";
import { console2 } from "forge-std/console2.sol";

import { RWAStaking } from "../src/RWAStaking.sol";
import { ReserveStaking } from "../src/ReserveStaking.sol";
import { SBTC } from "../src/SBTC.sol";
import { STONE } from "../src/STONE.sol";
import { PlumePreReserveFund } from "../src/proxy/PlumePreReserveFund.sol";
import { PlumePreStaking } from "../src/proxy/PlumePreStaking.sol";

contract DeployStakingContracts is Script {

    address private constant DEPLOYER_ADDRESS = ******************************************;
    address private constant MULTISIG_ADDRESS = ******************************************;
    address private constant USDC_ADDRESS = ******************************************;
    address private constant USDT_ADDRESS = ******************************************;
    address private constant SBTC_ADDRESS = ******************************************;
    address private constant STONE_ADDRESS = ******************************************;

    function test() public { }

    function run() external {
        vm.startBroadcast(DEPLOYER_ADDRESS);

        address[] memory proposers = new address[](1);
        address[] memory executors = new address[](1);
        proposers[0] = MULTISIG_ADDRESS;
        executors[0] = MULTISIG_ADDRESS;
        TimelockController timelock = new TimelockController(2 days, proposers, executors, address(0));

        RWAStaking rwaStaking = new RWAStaking();
        PlumePreStaking plumePreStaking = new PlumePreStaking(
            address(rwaStaking), abi.encodeCall(RWAStaking.initialize, (timelock, MULTISIG_ADDRESS))
        );
        RWAStaking(address(plumePreStaking)).allowStablecoin(IERC20(USDC_ADDRESS));
        RWAStaking(address(plumePreStaking)).allowStablecoin(IERC20(USDT_ADDRESS));
        console2.log("Plume Pre-Staking Proxy deployed to:", address(plumePreStaking));

        ReserveStaking sbtcStaking = new ReserveStaking();
        PlumePreReserveFund plumePreReserveFund = new PlumePreReserveFund(
            address(sbtcStaking),
            abi.encodeCall(
                ReserveStaking.initialize, (timelock, MULTISIG_ADDRESS, IERC20(SBTC_ADDRESS), IERC20(STONE_ADDRESS))
            )
        );
        console2.log("Plume Pre-Reserve Fund Proxy deployed to:", address(plumePreReserveFund));

        vm.stopBroadcast();
    }

}
