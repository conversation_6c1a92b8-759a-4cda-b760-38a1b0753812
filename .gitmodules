[submodule "arc/lib/openzeppelin-contracts"]
	path = arc/lib/openzeppelin-contracts
	url = https://github.com/OpenZeppelin/openzeppelin-contracts
[submodule "arc/lib/openzeppelin-contracts-upgradeable"]
	path = arc/lib/openzeppelin-contracts-upgradeable
	url = https://github.com/OpenZeppelin/openzeppelin-contracts-upgradeable
[submodule "plume/lib/solidstate-solidity"]
	path = plume/lib/solidstate-solidity
	url = https://github.com/solidstate-network/solidstate-solidity
[submodule "misc/lib/forge-std"]
	path = misc/lib/forge-std
	url = https://github.com/foundry-rs/forge-std
[submodule "misc/lib/openzeppelin-contracts-upgradeable"]
	path = misc/lib/openzeppelin-contracts-upgradeable
	url = https://github.com/OpenZeppelin/openzeppelin-contracts-upgradeable
[submodule "misc/lib/openzeppelin-foundry-upgrades"]
	path = misc/lib/openzeppelin-foundry-upgrades
	url = https://github.com/OpenZeppelin/openzeppelin-foundry-upgrades
[submodule "plume/lib/forge-std"]
	path = plume/lib/forge-std
	url = https://github.com/foundry-rs/forge-std
[submodule "plume/lib/openzeppelin-contracts-upgradeable"]
	path = plume/lib/openzeppelin-contracts-upgradeable
	url = https://github.com/OpenZeppelin/openzeppelin-contracts-upgradeable
[submodule "plume/lib/openzeppelin-foundry-upgrades"]
	path = plume/lib/openzeppelin-foundry-upgrades
	url = https://github.com/openzeppelin/openzeppelin-foundry-upgrades
[submodule "smart-wallets/lib/forge-std"]
	path = smart-wallets/lib/forge-std
	url = https://github.com/foundry-rs/forge-std
[submodule "smart-wallets/lib/openzeppelin-contracts-upgradeable"]
	path = smart-wallets/lib/openzeppelin-contracts-upgradeable
	url = https://github.com/OpenZeppelin/openzeppelin-contracts-upgradeable
[submodule "smart-wallets/lib/solmate"]
	path = smart-wallets/lib/solmate
	url = https://github.com/transmissions11/solmate
[submodule "staking/lib/forge-std"]
	path = staking/lib/forge-std
	url = https://github.com/foundry-rs/forge-std
[submodule "staking/lib/openzeppelin-contracts-upgradeable"]
	path = staking/lib/openzeppelin-contracts-upgradeable
	url = https://github.com/OpenZeppelin/openzeppelin-contracts-upgradeable
[submodule "staking/lib/openzeppelin-foundry-upgrades"]
	path = staking/lib/openzeppelin-foundry-upgrades
	url = https://github.com/OpenZeppelin/openzeppelin-foundry-upgrades
