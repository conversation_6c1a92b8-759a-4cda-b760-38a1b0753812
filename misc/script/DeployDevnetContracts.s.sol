// // SPDX-License-Identifier: MIT
// pragma solidity ^0.8.25;

// import { Script } from "forge-std/Script.sol";
// import { console2 } from "forge-std/console2.sol";

// import { Faucet } from "../src/Faucet.sol";
// import { FaucetProxy } from "../src/proxy/FaucetProxy.sol";

// contract DeployDevnetContracts is Script {

//     address private constant FAUCET_ADMIN_ADDRESS = ******************************************;
//     address private constant ETH_ADDRESS = address(1);
//     address private constant USDT_ADDRESS = ******************************************;

//     string[] private tokens = ["ETH", "USDT"];
//     address[] private tokenAddresses = [ETH_ADDRESS, USDT_ADDRESS];

//     function run() external {
//         vm.startBroadcast(FAUCET_ADMIN_ADDRESS);

//         Faucet faucet = new Faucet();
//         FaucetProxy faucetProxy = new FaucetProxy(
//             address(faucet), abi.encodeCall(Faucet.initialize, (FAUCET_ADMIN_ADDRESS, tokens, tokenAddresses))
//         );
//         console2.log("Faucet Proxy deployed to:", address(faucetProxy));

//         vm.stopBroadcast();
//     }

// }
