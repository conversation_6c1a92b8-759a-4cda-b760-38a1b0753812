{"name": "@solidstate/contracts", "version": "0.0.61", "author": "<PERSON>", "license": "MIT", "description": "Solidity library for flexible smart contract development", "keywords": ["solidity", "smart-contracts", "ethereum", "ether", "eth", "cryptocurrency", "crypto", "wow", "library", "hardhat", "buidler"], "repository": "github:solidstate-network/solidstate-solidity", "files": ["/**/*.sol", "!/**/*Mock.sol"], "publishConfig": {"access": "public"}}